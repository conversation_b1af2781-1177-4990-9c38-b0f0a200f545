const { ipc<PERSON><PERSON><PERSON> } = require('electron')
window.$ = window.jQuery = require('../vendor/jquery.js');
const btn = document.querySelector("#btn_paysheet")
const btn_send = document.querySelector("#btn_send")
let table;


class Payslip {
  constructor() {
    this.hookEvent()
    this.colTitleMap = {}
    $("#btn_paysheet").button()
    $("#btn_send").button({disabled: true})
    $("#paymonth").MonthPicker({ShowIcon: false})
  }
  hookEvent() {
    btn.addEventListener("click", (e)=>{
      ipcRenderer.send("show-dialog")
    })
    btn_send.addEventListener("click", (e)=>{
      ipcRenderer.send("send-email", {paymonth: $("#paymonth").val()});
      $("#btn_send").button({disabled: true})
      $("#btn_paysheet").button({disabled: true})
      $("#payslipTable .pending-send").waitMe({effect: 'ios', fontSize: '10px', maxSize: '30'})
    })
    let that = this
    ipcRenderer.on('send-email-reply', (event, arg) => {
      let sent = $("#payslipTable .pending-send[data-id=" + arg.id + "]")
      sent.waitMe()
      if (arg.isSuccess) {
        sent.css("color", "green")
        sent.text("发送成功")
      } else {
        sent.css("color", "red")
        sent.text("发送失败")
      }
      ipcRenderer.send("send-email", {paymonth: $("#paymonth").val()})
    })
    ipcRenderer.on('send-email-complete', (event, arg) => {
      $("#btn_paysheet").button({disabled: false})
    })
    ipcRenderer.on('show-dialog-reply', (event, arg) => {
      if (table) {
        table.destroy();
      } else {
        $("#payslipTable").css("display", "block")
        $(".content").css("background", "none")
      }
      table = $("#payslipTable").DataTable({
        columnDefs: [{
          targets: 23,
          render: function (data, type, row, meta) {
            return '<div class="pending-send" data-id="'+data+'">待发送</a>';
          }
        }],
        data: that.arraify(arg),
        "scrollY":        "600px",
        "scrollX": true,
        "scrollCollapse": true,
        "paging":         false,
        "ordering":       false,
        "searching":      false
      })
      $("#btn_send").button({
        disabled: false
      })
    })
  }

  arraify(data) {
    let resultList = []
    data.forEach(element => {
      let result = []
      result.push(element["姓名"])
      result.push(element["应发薪资"]) 
      result.push(element["补/扣项目"]) 
      result.push(element["绩效薪资"]) 
      result.push(element["餐补"]) 
      result.push(element["其他奖励"]) 
      result.push(element["其他津贴"])
      result.push(element["当月收入"]) 
      result.push(element["社保个人扣款合计"]) 
      result.push(element["公积金个人扣款合计"]) 
      result.push(element["附加专项扣除（累计）"]) 
      result.push(element["税前工资"]) 
      result.push(element["个人所得税"]) 
      result.push(element["实发工资"]) 
      result.push(element["补充医疗扣款"]) 
      result.push(element["最终工资"])
      result.push(element["年终绩效薪资"])
      result.push(element["基础年终奖"])
      result.push(element["利润贡献奖"])
      result.push(element["评优"])
      result.push(element["奖金合计"])
      result.push(element["个税"])
      result.push(element["奖金实发"])
      result.push(element["_id"])
      resultList.push(result)
    });
    return resultList
  }
  
  checkIfThereIsOngoingProcess() {

  }
  PerservePayslipData(payslipList) {
    
  }
}

$(()=>{
  new Payslip()
})

