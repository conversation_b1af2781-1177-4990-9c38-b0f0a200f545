const { app, BrowserWindow, dialog, ipcMain} = require('electron')
const path = require('path')
const Excel = require('exceljs');
const Datastore = require('nedb-promises');
const nodemailer = require('nodemailer');
const Email = require('email-templates');
const { type } = require('os');



const dataStartRow = 4
const sheetName = "明细表"
const validTitle = ["姓名", "绩效薪资", "核定薪资", "病假扣款", "事假扣款", "其他扣款", "加班", "餐补", "应发工资", "福利", "社保个人扣款", "公积金个人扣款", "附加专项扣除（累计）", "个人所得税", "税前工资", "实发工资", "最终工资", "年终奖", "年终奖个税", "实发年终奖", "邮箱"]
const colTitleMap = {}
let payslipListCache = []

let sendIndex = 0

const mailServer = "smtp.qiye.aliyun.com"
const mailPort = 25
const mailUsername = "<EMAIL>"
const mailPass = "Wang1024"


let win

function createWindow () {
  const htmlPath = path.join('src', 'index.html')

  win = new BrowserWindow({
    width: 100,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
    },
  })

  win.maximize()

  win.loadFile(htmlPath)

  win.on('close', function(event) { 
    event.preventDefault()
    let options = {}

    options.type = "question"
    options.buttons = ["退出","取消"]
    options.defaultId = 1
    options.title = "邮件发送中"
    options.message = "邮件发送中, 要强行退出吗?"
    options.detail = "请等待邮件发送完毕后再退出，以免漏发邮件，退出点击确定"
    options.cancelId = 1
    options.noLink = true
    options.normalizeAccessKeys = true

    dialog.showMessageBox(null, options).then(result => {
      if (result.response === 0) {
        win.destroy()
        app.quit()
      } else if (result.response === 1) {
      }
    })
    
  });

}

app.whenReady().then(() => {
  createWindow()
})

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit()
})

// try {
//   require('electron-reloader')(module)
// } catch (_) {}

const dbFactory = (fileName) => {
  return Datastore.create({
    filename: app.getAppPath('userData') + "/data/" + fileName
  });
}

const getMailTransporter = () => {
  // create reusable transporter object using the default SMTP transport
    let transporter = nodemailer.createTransport({
        host: mailServer,
        port: mailPort,
        secure: false, // true for 465, false for other ports
        auth: {
            user: mailUsername, // generated ethereal user
            pass: mailPass  // generated ethereal password
        }
    });
  
  return transporter
}

const transporter = getMailTransporter()

const db = dbFactory("data.db");

const readExcelData = (data, event) => {
    var workbook = new Excel.Workbook();
    workbook.xlsx.readFile(data.filePaths[0])
        .then(function() {
            var worksheet = workbook.getWorksheet(sheetName);
            let payslipList = [];
            let titleRow = worksheet.getRow(3)
            let cellvalue = "";
            titleRow.eachCell(function(cell, colNumber) {
              if(cell.value instanceof Object) {
                cellvalue = cell.value.richText[0].text;
              } else {
                cellvalue = cell.value
              }
              if(validTitle.includes(cellvalue)) {
                colTitleMap[colNumber] = cellvalue 
              }
            })

            worksheet.eachRow(function(row, rowNumber) {
              if(rowNumber<dataStartRow) return
              if(row.getCell(1).value == null || isNaN(row.getCell(1).value)) return
              let payslip = {}
              row.eachCell({includeEmpty: true}, function(cell, colNumber) {
                if(colTitleMap[colNumber]) {
                  let val;
                  if (cell.value) {
                    val = cell.value
                    if(cell.value.text) {
                      val = cell.value.text
                    } else if(cell.value.result) {
                      val = cell.value.result
                    } 
                    if(typeof val !== "string" && typeof val!=="number") {
                      val = ""
                    }
                  } else {
                    val = ""
                  }
                  payslip[colTitleMap[colNumber]] = val
                }
              });
              payslipList.push(payslip)
            });
            if (payslipList.length <= 0) {
              alert("没有发现工资数据，或数据不合法，请选择合法的工资数据文件")
            } else{
              preserveData(payslipList, event)
            }
  });
}

const preserveData = (payslipList, event) => {
  db.remove({}, { multi: true }).then(()=>{
    db.insert(payslipList).then(()=> {
      db.find({}).then(function(result){
        payslipListCache = result
        win.webContents.send('show-dialog-reply', payslipListCache)
      })
    }).catch((e)=>{
      console.log(e)
    })
  })
}

const formatMoney = (money) => {

}

ipcMain.on('show-dialog', function(event) {
  dialog.showOpenDialog().then((data) => {
    if(data.filePaths.length == 0){
      console.log("No file selected");
      return;
    }
    readExcelData(data, event)
 });
})

ipcMain.on('send-email', function(event, param) {
  if (sendIndex >= payslipListCache.length) {
    sendIndex=0;
    return win.webContents.send('send-email-complete', {size: sendIndex})
  }

  var templateDir = path.join(__dirname, 'emails', 'template1/html');

  let payslip = payslipListCache[sendIndex++]
  var emailTemplate = new Email(templateDir);
  var locals = {
        paymonth: param.paymonth,
        name: payslip["姓名"],
        performanceSalary:payslip["绩效薪资"],
        baseSalary: payslip["核定薪资"],
        illLeaveDeduct: payslip["病假扣款"],
        generalLeaveDeduct: payslip["事假扣款"],
        otherDeduct: payslip["其他扣款"],
        workOvertime: payslip["加班"],
        mealSubsidy: payslip["餐补"],
        ObtainedSalary: payslip["应发工资"],
        middleAutumnBonus: payslip["福利"],
        socialSecurityDeduct: payslip["社保个人扣款"],
        houseFundDeduct: payslip["公积金个人扣款"],
        exemption: payslip["附加专项扣除（累计）"],
        icomeTax: payslip["个人所得税"],
        beforeTax: payslip["税前工资"],
        afterTax: payslip["实发工资"],
        medicineCare: payslip["2021补充医疗扣款"],
        actualSalary: payslip["最终工资"],
        annualBonus: payslip["年终奖"],
        annualBonusTax: payslip["年终奖个税"],
        annualBonusAfterTax: payslip["实发年终奖"]
  }

  emailTemplate.render(templateDir, locals).then((html, error)=>{
    if (error) {
      return console.error(err)
    }
    // check here what is showing in your result
    transporter.sendMail({
        from: '<EMAIL>',
        to: payslip["邮箱"],
        subject: "工资单（"+param.paymonth+"）",
        html: html//,
        // text: results.text
    }, function (err, responseStatus) {
        setTimeout(()=>{
          if (err) {
            win.webContents.send('send-email-reply', {isSuccess: false, id: payslip["_id"]}) 
          } else {
            win.webContents.send('send-email-reply', {isSuccess: true, id: payslip["_id"]}) 
          }
        }, 3000);
        
        return responseStatus;// return from status or as you need;
    })
  })
  .catch((e)=>{
    console.log(e)
  });
})


