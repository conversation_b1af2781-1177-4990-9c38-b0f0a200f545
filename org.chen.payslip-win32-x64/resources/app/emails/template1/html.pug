br
div
  div(style='text-align:center;font-size:23px; font-weight:bold;color:#2e2e2e') 工资单（保密）
  br
  br
  div 工资年月:&nbsp;&nbsp;&nbsp;&nbsp;#{paymonth}
  table(style='width: 100%; border: 2px solid black; border-collapse: collapse')
    thead
      tr(role='row', style='border: 2px solid black; background-color: #d9d9d9; font-size:15px;')
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x59D3;&#x540D;
        th(style='border: 1px solid #999999; padding:5px;')
          div 绩效薪资
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x6838;&#x5B9A;&#x85AA;&#x8D44;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x75C5;&#x5047;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x4E8B;&#x5047;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x5176;&#x4ED6;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x52A0;&#x73ED;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x9910;&#x8865;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x5E94;&#x53D1;&#x5DE5;&#x8D44;
        th(style='border: 1px solid #999999; padding:5px;')
          div 福利 
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x793E;&#x4FDD;&#x4E2A;&#x4EBA;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x516C;&#x79EF;&#x91D1;&#x4E2A;&#x4EBA;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x9644;&#x52A0;&#x4E13;&#x9879;&#x6263;&#x9664;&#xFF08;&#x7D2F;&#x8BA1;&#xFF09;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x4E2A;&#x4EBA;&#x6240;&#x5F97;&#x7A0E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x7A0E;&#x524D;&#x5DE5;&#x8D44;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x5B9E;&#x53D1;&#x5DE5;&#x8D44;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x8865;&#x5145;&#x533B;&#x7597;&#x6263;&#x6B3E;
        th(style='border: 1px solid #999999; padding:5px;')
          div &#x6700;&#x7EC8;&#x5DE5;&#x8D44;
        th(style='border: 1px solid #999999; padding:5px;')
          div 年终奖
        th(style='border: 1px solid #999999; padding:5px;')
          div 年终奖个税
        th(style='border: 1px solid #999999; padding:5px;')
          div 实发年终奖
    tbody
      tr(style='text-align:center; color: #2e2e2e;')
        td(style='border: 1px dotted #999999; padding:5px;') #{name}
        td(style='border: 1px dotted #999999; padding:5px;') #{performanceSalary}
        td(style='border: 1px dotted #999999; padding:5px;') #{baseSalary}
        td(style='border: 1px dotted #999999; padding:5px;') #{illLeaveDeduct}
        td(style='border: 1px dotted #999999; padding:5px;') #{generalLeaveDeduct}
        td(style='border: 1px dotted #999999; padding:5px;') #{otherDeduct}
        td(style='border: 1px dotted #999999; padding:5px;') #{workOvertime}
        td(style='border: 1px dotted #999999; padding:5px;') #{mealSubsidy}
        td(style='border: 1px dotted #999999; padding:5px;') #{ObtainedSalary}
        td(style='border: 1px dotted #999999; padding:5px;') #{middleAutumnBonus}
        td(style='border: 1px dotted #999999; padding:5px;') #{socialSecurityDeduct}
        td(style='border: 1px dotted #999999; padding:5px;') #{houseFundDeduct}
        td(style='border: 1px dotted #999999; padding:5px;') #{exemption}
        td(style='border: 1px dotted #999999; padding:5px;') #{icomeTax}
        td(style='border: 1px dotted #999999; padding:5px;') #{beforeTax}
        td(style='border: 1px dotted #999999; padding:5px;') #{afterTax}
        td(style='border: 1px dotted #999999; padding:5px;') #{medicineCare}
        td(style='border: 1px dotted #999999; padding:5px; color: black;font-weight: bold') #{actualSalary}
        td(style='border: 1px dotted #999999; padding:5px;') #{annualBonus}
        td(style='border: 1px dotted #999999; padding:5px;') #{annualBonusTax}
        td(style='border: 1px dotted #999999; padding:5px;') #{annualBonusAfterTax}
br
p
  span(style='font-weight:bold;') 工资说明:
  | 核定工资是每月工资标准（包括基本【核定工资80%】、岗位【核定工资15-10%】、绩效【核定工资5-10%】）；午餐补助20元/天（按照当月法定实际出勤天数计算）；迟到一次50元；病假扣日工资40%。